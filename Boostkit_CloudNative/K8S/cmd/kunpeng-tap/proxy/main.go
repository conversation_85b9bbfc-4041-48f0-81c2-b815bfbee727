/*
Copyright 2025.

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

    http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.
*/

package main

import (
	"context"
	"flag"
	"os"
	"os/signal"
	"path/filepath"

	"github.com/docker/docker/client"
	"google.golang.org/grpc"
	"google.golang.org/grpc/credentials/insecure"
	criv1 "k8s.io/cri-api/pkg/apis/runtime/v1"
	"k8s.io/klog/v2"

	"kunpeng.huawei.com/kunpeng-cloud-computing/cmd/kunpeng-tap/proxy/options"
	"kunpeng.huawei.com/kunpeng-cloud-computing/pkg/kunpeng-tap/cache"
	"kunpeng.huawei.com/kunpeng-cloud-computing/pkg/kunpeng-tap/monitoring"
	"kunpeng.huawei.com/kunpeng-cloud-computing/pkg/kunpeng-tap/policy"
	numa_aware "kunpeng.huawei.com/kunpeng-cloud-computing/pkg/kunpeng-tap/policy/numa-aware"
	topologyaware "kunpeng.huawei.com/kunpeng-cloud-computing/pkg/kunpeng-tap/policy/topology-aware"
	"kunpeng.huawei.com/kunpeng-cloud-computing/pkg/kunpeng-tap/server"
	"kunpeng.huawei.com/kunpeng-cloud-computing/pkg/kunpeng-tap/server/containerd"
	"kunpeng.huawei.com/kunpeng-cloud-computing/pkg/kunpeng-tap/server/dispatcher"
	dispatch "kunpeng.huawei.com/kunpeng-cloud-computing/pkg/kunpeng-tap/server/dispatcher"
	"kunpeng.huawei.com/kunpeng-cloud-computing/pkg/kunpeng-tap/server/docker"
	"kunpeng.huawei.com/kunpeng-cloud-computing/pkg/kunpeng-tap/version"
)

func main() {
	parseFlags()

	if options.Version {
		version.PrintVersion()
		return
	}

	validateOptions()
	setupRuntimeEndpoint()

	cache := createCache()
	policyManager := createPolicyManager(cache)
	dispatcher := dispatch.NewDispatcher(policyManager, cache)
	proxyServer := createProxyServer(dispatcher, cache)

	startServer(proxyServer)

	c := make(chan os.Signal, 1)
	signal.Notify(c, os.Interrupt)
	<-c

	ctx, cancel := context.WithTimeout(context.Background(), options.GracefulTimeout)
	defer cancel()

	proxyServer.Shutdown(ctx)
	klog.InfoS("Proxy server shutting down")
	os.Exit(0)
}

// parseFlags initializes and parses command line flags
func parseFlags() {
	flag.StringVar(&options.RuntimeProxyEndpoint, "runtime-proxy-endpoint", options.DefaultRuntimeProxyEndpoint,
		"runtimeproxy service endpoint.")
	flag.StringVar(&options.ContainerRuntimeEndpoint, "container-runtime-service-endpoint", options.DefaultDockerRuntimeServiceEndpoint,
		"container runtime service endpoint.")
	flag.StringVar(&options.ContainerRuntimeMode, "container-runtime-mode", options.DefaultContainerRuntimeMode,
		"container engine(Containerd|Docker).")
	flag.StringVar(&options.ResourcePolicy, "resource-policy", options.DefaultResourcePolicy,
		"container resource policy(numa-aware|topology-aware).")
	flag.StringVar(&options.ResourcePriority, "resource-priority", options.DefaultResourcePriority,
		"resource allocation priority for topology-aware policy(cpu-first|gpu-first). Default cpu-first")
	flag.DurationVar(&options.GracefulTimeout, "graceful-timeout", options.DefaultGracefulTimeout,
		"the duration for which the server gracefully wait for existing connections to finish, default 15s.")
	flag.StringVar(&options.MetricsAddr, "metrics-bind-address", ":9091", "The address the metrics endpoint binds to. Default :9091")
	flag.BoolVar(&options.Version, "version", false, "The version of kunpeng-tap proxy")
	flag.BoolVar(&options.EnableMemoryTopology, "enable-memory-topology", false,
		"Enable memory topology awareness. Default false")

	klog.InitFlags(nil)
	flag.Parse()
}

// validateOptions validates command line options
func validateOptions() {
	if options.ResourcePriority != options.ResourcePriorityCPUFirst &&
		options.ResourcePriority != options.ResourcePriorityGPUFirst {
		klog.Fatalf("invalid resource priority %v, must be one of: %v, %v",
			options.ResourcePriority, options.ResourcePriorityCPUFirst, options.ResourcePriorityGPUFirst)
	}
}

// setupRuntimeEndpoint prepares the runtime proxy endpoint
func setupRuntimeEndpoint() {
	if err := os.Remove(options.RuntimeProxyEndpoint); err != nil && !os.IsNotExist(err) {
		klog.Fatalf("failed to unlink %v: %v", options.RuntimeProxyEndpoint, err)
	}

	if err := os.MkdirAll(filepath.Dir(options.RuntimeProxyEndpoint), 0755); err != nil {
		klog.Fatalf("failed to mkdir %v: %v", filepath.Dir(options.RuntimeProxyEndpoint), err)
	}
}

// createCache creates and returns a new cache instance
func createCache() cache.Cache {
	cache, err := cache.NewCache(cache.Options{CacheDir: "/topology_cache"})
	if err != nil {
		klog.Fatalf("failed to create cache: %v", err)
	}
	return cache
}

// createPolicyManager creates and configures the policy manager
func createPolicyManager(cache cache.Cache) policy.HookManager {
	policyOpts := createPolicyOptions()

	switch options.ResourcePolicy {
	case options.TopologyAwarePolicy:
		return policy.NewPolicyManagerWithPolicies(cache, []policy.Policy{
			topologyaware.NewTopologyAwarePolicy(cache, policyOpts),
		})
	case options.NumaAwarePolicy:
		return policy.NewPolicyManagerWithPolicies(cache, []policy.Policy{
			numa_aware.NewNumaAwarePolicy(cache),
		})
	default:
		klog.Fatalf("unknown resource policy %v", options.ResourcePolicy)
		return nil
	}
}

// createPolicyOptions creates and configures policy options
func createPolicyOptions() *policy.PolicyOptions {
	policyOpts := policy.NewPolicyOptions()
	policyOpts.EnableMemoryTopology = options.EnableMemoryTopology

	switch options.ResourcePriority {
	case options.ResourcePriorityGPUFirst:
		policyOpts.ResourcePriority = policy.ResourcePriorityGPUFirst
		klog.InfoS("Using GPU-first resource allocation strategy")
	case options.ResourcePriorityCPUFirst:
		fallthrough
	default:
		policyOpts.ResourcePriority = policy.ResourcePriorityCPUFirst
		klog.InfoS("Using CPU-first resource allocation strategy")
	}

	return policyOpts
}

// createProxyServer creates the appropriate proxy server based on runtime mode
func createProxyServer(dispatcher dispatcher.Dispatcher, cache cache.Cache) server.ProxyServer {
	switch options.ContainerRuntimeMode {
	case options.ContainerRuntimeModeContainerd:
		return NewContainerdProxyServer(dispatcher, cache)
	case options.ContainerRuntimeModeDocker:
		return NewDockerProxyServer(dispatcher, cache)
	default:
		klog.Fatalf("unknown runtime engine backend %v", options.ContainerRuntimeMode)
		return nil
	}
}

// startServer starts the proxy server and monitoring
func startServer(proxyServer server.ProxyServer) {
	go monitoring.ExportMetrics()
	monitoring.ProxyHealthz.Set(1)
	go proxyServer.Run()
}

func NewContainerdProxyServer(dispatcher dispatcher.Dispatcher, cache cache.Cache) server.ProxyServer {
	containerRuntimeConn, err := grpc.NewClient(
		options.GRPCPassthroughScheme+options.ContainerRuntimeEndpoint,
		grpc.WithTransportCredentials(insecure.NewCredentials()),
		grpc.WithContextDialer(containerd.Dialer),
	)
	if err != nil {
		klog.Fatalf("fail to create runtime service client %v", err)
	}

	runtimeServiceClient := criv1.NewRuntimeServiceClient(containerRuntimeConn)
	// According to the version of cri api supported by backend runtime, create the corresponding cri server.
	if _, err := runtimeServiceClient.Version(context.Background(), &criv1.VersionRequest{}); err != nil {
		klog.Fatalf("fail to create runtime service client %v", err)
	}

	containerdClient := criv1.NewRuntimeServiceClient(containerRuntimeConn)

	err = cache.LoadStoreContainerd(containerdClient)
	if err != nil {
		klog.Fatalf("failed to load container and pod into cache: %v", err)
	}

	// 默认使用NUMA-aware策略
	return containerd.NewContainerdServer(
		containerd.NewCriServer(runtimeServiceClient, dispatcher),
		containerRuntimeConn,
	)
}

func NewDockerProxyServer(dispatcher dispatcher.Dispatcher, cache cache.Cache) server.ProxyServer {
	dockerClient, err := client.NewClientWithOpts(client.WithHost(options.UnixSocketPrefix+options.ContainerRuntimeEndpoint), client.WithAPIVersionNegotiation())
	if err != nil {
		klog.Fatalf("failed to get docker client from %v: %v", options.UnixSocketPrefix+options.ContainerRuntimeEndpoint, err)
	}

	info, err := dockerClient.Info(context.Background())
	if err != nil {
		klog.Fatalf("failed to get docker info: %v", err)
	}

	err = cache.LoadStoreDocker(dockerClient, info.CgroupDriver)
	if err != nil {
		klog.Fatalf("failed to load container and pod into cache: %v", err)
	}

	dispatcher.SetDockerCgroupDriver(info.CgroupDriver)
	// 默认使用NUMA-aware策略
	return docker.NewDockerServer(docker.NewDockerHandler(
		docker.ReverseProxy(options.ContainerRuntimeEndpoint),
		cache,
		dockerClient,
		dispatcher,
	))
}
