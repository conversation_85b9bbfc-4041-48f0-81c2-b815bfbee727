# Resource Priority Configuration for Topology-Aware Policy

This document demonstrates how to use the new resource priority feature in the topology-aware policy.

## Overview

The topology-aware policy now supports configuring resource allocation priority between CPU and GPU resources. This allows users to optimize container placement based on their workload characteristics.

## Configuration Options

### CPU-First Priority (Default)
When using CPU-first priority, the scheduler will:
1. First compare CPU capacity between NUMA nodes
2. Then consider GPU affinity if CPU capacity is equal

### GPU-First Priority
When using GPU-first priority, the scheduler will:
1. First compare GPU affinity between NUMA nodes
2. Then consider CPU capacity if GPU affinity is equal

## Command Line Usage

### Using CPU-First Priority (Default)
```bash
./kunpeng-tap --resource-policy=topology-aware --resource-priority=cpu-first
```

### Using GPU-First Priority
```bash
./kunpeng-tap --resource-policy=topology-aware --resource-priority=gpu-first
```

### Help Information
```bash
./kunpeng-tap --help
```

## Example Scenarios

### Scenario 1: Mixed Workload Environment
In an environment with both CPU-intensive and GPU-intensive workloads:

1. For CPU-intensive pods:
   ```bash
   ./kunpeng-tap --resource-priority=cpu-first
   ```

2. For GPU-intensive pods:
   ```bash
   ./kunpeng-tap --resource-priority=gpu-first
   ```

### Scenario 2: Machine Learning Cluster
For a dedicated ML cluster where GPU locality is critical:
```bash
./kunpeng-tap --resource-policy=topology-aware --resource-priority=gpu-first --enable-memory-topology
```

## Validation

### Check Current Configuration
The proxy will log the current resource priority strategy on startup:
- `Using CPU-first resource allocation strategy`
- `Using GPU-first resource allocation strategy`

### Parameter Validation
Invalid priority values will result in an error:
```bash
./kunpeng-tap --resource-priority=invalid-option
# Output: invalid resource priority invalid-option, must be one of: cpu-first, gpu-first
```

## Implementation Details

The resource priority affects the comparison order in the topology-aware policy's node selection algorithm:

1. **Resource capacity check** (unchanged)
2. **Depth comparison** (unchanged)
3. **Shared capacity comparison** (unchanged)
4. **Colocated containers comparison** (unchanged)
5. **Resource priority strategy** (new)
   - CPU-first: Compare CPU capacity, then GPU affinity
   - GPU-first: Compare GPU affinity, then CPU capacity
6. **Final tie-breaker** (unchanged)

This ensures that the new feature integrates seamlessly with existing allocation logic while providing the flexibility to prioritize different resource types based on workload requirements.
