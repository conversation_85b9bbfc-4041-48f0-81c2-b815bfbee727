# Resource Priority Functional Test Documentation

## 概述

本文档详细描述了Kunpeng-TAP topology-aware策略中资源优先级功能的测试配置、测试场景和结果验证。该功能允许用户配置CPU优先或GPU优先的资源分配策略，以优化不同类型工作负载的容器放置。

## 测试环境配置

### 硬件拓扑

**基础配置：**
- **CPU配置**：96核CPU，分布在4个NUMA节点上
- **NUMA配置**：4个NUMA节点，每个节点24个CPU核心
- **Socket配置**：2个Socket，每个Socket包含2个NUMA节点

**CPU分布：**
```
NUMA 0: CPUs 0-23   (Socket 0)
NUMA 1: CPUs 24-47  (Socket 0)
NUMA 2: CPUs 48-71  (Socket 1)
NUMA 3: CPUs 72-95  (Socket 1)
```

### GPU配置场景

#### 场景1：2GPU分布（2GPU_NUMA1_3）
```
GPU 0: 位于NUMA 1 (PCI: 0000:01:00.0)
GPU 1: 位于NUMA 3 (PCI: 0000:02:00.0)
```

#### 场景2：4GPU分布（4GPU_ALL_NUMA）
```
GPU 0: 位于NUMA 0 (PCI: 0000:01:00.0)
GPU 1: 位于NUMA 1 (PCI: 0000:02:00.0)
GPU 2: 位于NUMA 2 (PCI: 0000:03:00.0)
GPU 3: 位于NUMA 3 (PCI: 0000:04:00.0)
```

### 容器规格配置

**测试容器类型：**
1. **小型容器**：4 CPU, 100MB内存
2. **中型容器**：8 CPU, 100MB内存
3. **大型容器**：12-20 CPU, 100MB内存
4. **GPU容器**：4-8 CPU + 指定GPU设备

**GPU环境变量格式：**
- 华为格式：`VA_VISIBLE_DEVICES=/dev/vacc0,/dev/vacc1`

## 测试场景详述

### 1. CPU优先策略测试

#### 1.1 CPU-only工作负载测试
**测试目标：** 验证CPU优先策略下，CPU-only容器优先分配到CPU容量最佳的NUMA节点

**测试配置：**
- 策略：`ResourcePriorityCPUFirst`
- 容器：4 CPU, 无GPU请求
- GPU环境：2GPU分布在NUMA1和NUMA3

**验证方法：**
- 提取分配的CPU集合并转换为NUMA节点
- 验证单NUMA分配时必须为NUMA 0
- 验证多NUMA分配时必须包含NUMA 0

**预期结果：**
- 容器应分配到NUMA 0（最佳CPU可用性）
- 不受GPU分布影响

**测试输出示例：**
```
CPU-only container (CPU-first strategy) allocated to NUMA nodes: [0]
Expected: Should prefer NUMA 0 (best CPU availability in fresh system)
✓ CPU-only container allocated to NUMA 0
```

#### 1.2 GPU工作负载测试
**测试目标：** 验证CPU优先策略下，先考虑CPU容量，再考虑GPU亲和性

**测试配置：**
- 策略：`ResourcePriorityCPUFirst`
- 容器：4 CPU + GPU 0请求
- GPU环境：GPU 0位于NUMA1

**验证方法：**
- 检查分配的NUMA节点是否为NUMA 0或NUMA 1
- 输出具体的策略决策原因
- 验证两种分配结果都是合理的

**预期结果：**
- NUMA 0：CPU容量优先（主要因素）
- NUMA 1：GPU亲和性优先（次要因素）
- 两种结果都是可接受的

**测试输出示例：**
```
GPU container (CPU-first) requesting GPU 0 allocated to NUMA nodes: [1]
Expected: GPU 0 is located on NUMA 1, but CPU-first may prefer NUMA 0 (best CPU capacity)
✓ CPU-first strategy: Allocated to NUMA 1 (GPU affinity as secondary factor)
```

#### 1.3 多GPU容器测试（2GPU场景）
**测试目标：** 验证CPU优先策略下多个GPU容器的分配行为

**测试配置：**
- 策略：`ResourcePriorityCPUFirst`
- 容器1：4 CPU + GPU 0请求（GPU 0位于NUMA1）
- 容器2：4 CPU + GPU 1请求（GPU 1位于NUMA3）
- GPU环境：2GPU分布在NUMA1和NUMA3

**验证方法：**
- 验证每个GPU容器的NUMA分配
- 确认CPU优先策略下的决策逻辑
- 验证两个容器分配到不同NUMA节点

**预期结果：**
- GPU 0容器：NUMA 0（CPU优先）或NUMA 1（GPU亲和性）
- GPU 1容器：NUMA 0（CPU优先）或NUMA 3（GPU亲和性）
- 两种分配结果都是可接受的

### 2. 分配验证策略

#### 2.1 基础验证
- **CPU容量验证**：检查分配的CPU数量是否符合请求
- **内存分配验证**：确认内存分配符合要求
- **资源隔离验证**：确保不同容器的资源分配不冲突

#### 2.2 NUMA亲和性验证
- **单NUMA分配**：验证容器分配到单个NUMA节点时的正确性
- **多NUMA分配**：验证跨NUMA分配时包含预期的NUMA节点
- **策略一致性**：确保分配结果符合资源优先级策略

#### 2.3 GPU亲和性验证
- **GPU设备映射**：确认GPU容器分配到GPU所在的NUMA节点
- **环境变量解析**：验证华为GPU环境变量的正确解析
- **无效GPU处理**：测试无效GPU请求的优雅处理

### 3. 测试输出分析

#### 3.1 成功分配输出
```
CPU-only container (CPU-first strategy) allocated to NUMA nodes: [0]
Expected: Should prefer NUMA 0 (best CPU availability in fresh system)
✓ CPU-only container allocated to NUMA 0
```

#### 3.2 策略决策输出
```
GPU container (CPU-first) requesting GPU 0 allocated to NUMA nodes: [1]
Expected: GPU 0 is located on NUMA 1, but CPU-first may prefer NUMA 0 (best CPU capacity)
✓ CPU-first strategy: Allocated to NUMA 1 (GPU affinity as secondary factor)
```

#### 3.3 系统日志分析
关键日志信息：
```
"Allocated resources for container" container=... grant="<Grant: node NUMA node #X, ...>"
"VA_VISIBLE_DEVICES":"/dev/vacc0" // GPU环境变量解析
"Claims done, Start to Check GPU" // GPU检查流程
```

### 4. 验证标准

#### 4.1 CPU优先策略验证
- CPU-only容器：必须优先分配到NUMA 0
- GPU容器：NUMA 0（CPU优先）或GPU所在NUMA（亲和性）都可接受
- 多NUMA分配：必须包含NUMA 0或GPU所在NUMA

#### 4.2 GPU优先策略验证
- GPU容器：必须优先分配到GPU所在的NUMA节点
- CPU-only容器：必须回退到NUMA 0（最佳CPU可用性）
- 多GPU场景：每个GPU容器分配到对应GPU的NUMA节点

#### 4.3 边界情况验证
- 无效GPU请求：系统稳定，分配到默认NUMA节点
- 资源不足：正确返回错误信息
- 系统容器：不受资源优先级策略影响

## 测试执行

### 运行命令

#### 基础测试运行
```bash
# 运行所有topology-aware测试
go test ./test/kunpeng-tap/policy/topology-aware/ -v

# 运行特定的资源优先级功能测试
go test ./test/kunpeng-tap/policy/topology-aware/ -v -run "TestResourcePriorityFunctional"
```

#### 详细输出测试
```bash
# 运行测试并显示详细的Ginkgo输出（包括测试过程输出）
go test ./test/kunpeng-tap/policy/topology-aware/ -v -ginkgo.v

# 运行特定测试场景
go test ./test/kunpeng-tap/policy/topology-aware/ -v -ginkgo.focus="CPU-First Priority Strategy"
go test ./test/kunpeng-tap/policy/topology-aware/ -v -ginkgo.focus="GPU-First Priority Strategy"

# 运行新增的多GPU容器测试
go test ./test/kunpeng-tap/policy/topology-aware/ -v -ginkgo.focus="should handle multiple GPU containers"
go test ./test/kunpeng-tap/policy/topology-aware/ -v -ginkgo.focus="should allocate multiple GPU containers to their respective NUMA nodes"

# 运行新增的并发部署测试
go test ./test/kunpeng-tap/policy/topology-aware/ -v -ginkgo.focus="should handle concurrent deployment of multiple GPU containers"
go test ./test/kunpeng-tap/policy/topology-aware/ -v -ginkgo.focus="should deploy multiple GPU containers with optimal GPU affinity"
```

#### 调试和故障排除
```bash
# 运行测试并显示所有日志
go test ./test/kunpeng-tap/policy/topology-aware/ -v -ginkgo.v -args -logtostderr=true -v=2

# 运行单个测试用例
go test ./test/kunpeng-tap/policy/topology-aware/ -v -ginkgo.focus="should prioritize CPU capacity over GPU affinity for CPU-only workloads"
```

### 测试输出解读

#### 成功测试输出示例

**基础策略测试：**
```
Resource Priority Functional Tests CPU-First Priority Strategy with 2 GPUs on NUMA 1 and 3 should prioritize CPU capacity over GPU affinity for CPU-only workloads
  CPU-only container (CPU-first strategy) allocated to NUMA nodes: [0]
  Expected: Should prefer NUMA 0 (best CPU availability in fresh system)
  ✓ CPU-only container allocated to NUMA 0
• [0.001 seconds]
```

**多GPU容器测试：**
```
Resource Priority Functional Tests CPU-First Priority Strategy with 2 GPUs on NUMA 1 and 3 should handle multiple GPU containers with CPU-first priority
  GPU 0 container (CPU-first) allocated to NUMA nodes: [1]
  GPU 1 container (CPU-first) allocated to NUMA nodes: [3]
  ✓ GPU 0 container allocated to NUMA 1 (GPU affinity as secondary factor)
  ✓ GPU 1 container allocated to NUMA 3 (GPU affinity as secondary factor)
  ✓ Multiple GPU containers handled with CPU-first priority: GPU 0 on NUMA [1], GPU 1 on NUMA [3]
• [0.002 seconds]
```

**GPU优先策略多容器测试：**
```
Resource Priority Functional Tests GPU-First Priority Strategy with 4 GPUs on all NUMA nodes should allocate multiple GPU containers to their respective NUMA nodes
  GPU 0 container (GPU-first) allocated to NUMA nodes: [0]
  Expected: Should be allocated to NUMA 0 (where GPU 0 is located in 4GPU config)
  GPU 2 container (GPU-first) allocated to NUMA nodes: [2]
  Expected: Should be allocated to NUMA 2 (where GPU 2 is located in 4GPU config)
  ✓ GPU 0 container allocated to NUMA 0
  ✓ GPU 2 container allocated to NUMA 2
  ✓ Verified: GPU 0 container on NUMA [0], GPU 2 container on NUMA [2]
• [0.002 seconds]
```

**并发部署测试：**
```
Resource Priority Functional Tests CPU-First Priority Strategy with 2 GPUs on NUMA 1 and 3 should handle concurrent deployment of multiple GPU containers
  Deploying ML training container (GPU 0)...
  ML training container (GPU 0) allocated to NUMA nodes: [1]
  Deploying inference service container (GPU 1)...
  Inference service container (GPU 1) allocated to NUMA nodes: [3]
  Deploying data processing container (GPU 0, competing resource)...
  Data processing container (GPU 0) allocated to NUMA nodes: [0]
  ✓ ML training container allocated to NUMA 1 (GPU affinity)
  ✓ Inference service container allocated to NUMA 3 (GPU affinity)
  ✓ Data processing container allocated to NUMA 0 (CPU capacity priority)
  ✓ Successfully deployed multiple GPU containers concurrently
• [0.003 seconds]
```

**GPU优先策略并发部署测试：**
```
Resource Priority Functional Tests GPU-First Priority Strategy with 2 GPUs on NUMA 1 and 3 should deploy multiple GPU containers with optimal GPU affinity
  Deploying deep learning training container (GPU 0)...
  Deep learning training container (GPU 0) allocated to NUMA nodes: [1]
  Deploying computer vision inference container (GPU 1)...
  Computer vision inference container (GPU 1) allocated to NUMA nodes: [3]
  Deploying second training container (GPU 0, competing)...
  Second training container (GPU 0) allocated to NUMA nodes: [0]
  Deploying batch processing container (GPU 1, competing)...
  Batch processing container (GPU 1) allocated to NUMA nodes: [2]
  ✓ Deep learning training container allocated to NUMA 1
  ✓ Computer vision inference container allocated to NUMA 3
  ✓ Second training container allocated to NUMA 0 (resource competition fallback)
  ✓ Batch processing container allocated to NUMA 2 (resource competition fallback)
  ✓ Successfully deployed multiple GPU containers with optimal GPU affinity
• [0.003 seconds]
```

#### 关键输出元素
- **测试描述**：清楚说明测试的目标和场景
- **分配结果**：显示实际分配的NUMA节点
- **期望说明**：解释为什么这样分配是合理的
- **验证标记**：✓表示验证成功，!表示意外情况
- **执行时间**：测试用例的执行时间

### 成功标准

#### 功能正确性
- 所有48个测试用例通过（包括新增的多GPU容器并发部署测试）
- CPU优先策略正确优先考虑CPU容量
- GPU优先策略正确优先考虑GPU亲和性
- 混合工作负载场景处理正确
- 多GPU容器并发分配正确
- 不同GPU配置下的映射关系正确
- 并发部署场景下的资源竞争处理正确
- 智能回退机制工作正常

#### 输出验证
- 每个测试都有明确的NUMA节点分配输出
- 策略决策原因清楚说明
- 成功/失败标记正确显示
- 期望结果与实际结果一致

#### 稳定性要求
- 边界情况处理稳定
- 错误恢复机制正常
- 资源竞争处理合理
- 向后兼容性保持

## 测试覆盖率

### 功能覆盖
- ✅ CPU优先策略的所有分支和决策路径
- ✅ GPU优先策略的所有分支和回退机制
- ✅ 混合工作负载场景（CPU密集型+GPU密集型）
- ✅ 资源竞争和高负载处理
- ✅ 边界情况和错误恢复处理
- ✅ ML训练场景的多GPU分配
- ✅ 多GPU容器并发分配测试（2GPU和4GPU场景）
- ✅ GPU-NUMA映射关系验证
- ✅ 多GPU容器并发部署测试（真实生产场景模拟）
- ✅ 资源竞争和智能回退机制验证

### 配置覆盖
- ✅ 2GPU分布（NUMA1+NUMA3）和4GPU分布（全NUMA）
- ✅ 不同容器规格（小型4CPU、中型6-8CPU、大型12-20CPU）
- ✅ 华为GPU环境变量格式
- ✅ 系统容器和用户容器的差异化处理
- ✅ 无效GPU请求和边界条件
- ✅ 多GPU容器并发请求场景
- ✅ 不同GPU配置下的映射关系测试
- ✅ 真实生产环境工作负载模拟（ML训练、推理服务、数据处理、批处理）
- ✅ 资源竞争场景下的策略行为测试


## 已知限制

1. **Mock环境限制**：测试使用Mock系统，可能与真实硬件环境有差异
2. **GPU设备模拟**：GPU设备为模拟设备，不涉及真实GPU驱动
3. **并发测试增强**：新增了多GPU容器并发分配测试，但仍需更多复杂并发场景
4. **性能测试**：主要关注功能正确性，性能和延迟测试相对有限

## 后续改进建议

### 1. 真实环境验证
- **真实硬件测试**：在真实的多NUMA+多GPU环境中验证功能
- **不同GPU厂商**：测试华为等不同厂商GPU的兼容性
- **混合GPU环境**：测试同时存在不同类型GPU的复杂环境

### 2. 性能和规模测试
- **性能基准测试**：添加资源分配性能和延迟的基准测试
- **大规模测试**：测试数百个容器的并发分配场景
- **压力测试**：测试系统在高负载下的稳定性和性能
- **多GPU并发性能**：基于新增测试，扩展到更多GPU容器的并发分配性能测试
- **GPU亲和性性能影响**：测试GPU亲和性对实际应用性能的影响

### 3. 高级功能测试
- **动态策略切换**：测试运行时策略切换的影响
- **资源热插拔**：测试GPU设备动态添加/移除的处理
- **故障恢复**：添加硬件故障和网络分区的恢复测试
- **复杂多GPU场景**：基于当前2GPU和4GPU测试，扩展到8GPU、16GPU等更复杂场景
- **GPU资源竞争**：测试多个容器竞争同一GPU时的处理机制

### 4. 自动化和CI/CD集成
- **自动化测试流水线**：集成到CI/CD中进行回归测试
- **性能回归检测**：自动检测性能退化
- **测试覆盖率监控**：持续监控和改进测试覆盖率
