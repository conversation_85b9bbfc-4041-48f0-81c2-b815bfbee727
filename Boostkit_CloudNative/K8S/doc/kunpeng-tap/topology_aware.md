# Topology-Aware 策略设计与实现

## 概述

Topology-Aware策略是针对鲲鹏芯片架构的容器性能优化策略，通过感知硬件拓扑结构，实现CPU、内存、GPU设备的NUMA亲和性分配，最大化容器应用的性能表现。

### 核心目标

该策略专门针对鲲鹏芯片的多层次架构特性，优化以下关键资源的分配：

- **CPU资源**：基于Socket、Die、NUMA节点的层次化CPU分配，确保容器获得最佳的CPU亲和性
- **内存资源**：利用NUMA内存拓扑，实现内存本地化访问，减少跨NUMA访问延迟
- **GPU设备**：智能识别GPU与NUMA节点的亲和关系，优先将GPU容器调度到GPU所在的NUMA节点

通过硬件拓扑感知和资源亲和性优化，显著提升容器应用在鲲鹏平台上的执行效率。

## 设计架构

### 芯片架构信息获取

策略通过系统发现机制自动感知鲲鹏芯片的硬件拓扑：

````go
func (s *system) Discover() error {
    // Discover CPUs
    if err := s.discoverCPUs(); err != nil {
        return err
    }
    if err := s.discoverNodes(); err != nil {
        return err
    }
    if err := s.discoverPackages(); err != nil {
        return err
    }
    // 发现GPU设备
    if err := s.discoverGPUs(); err != nil {
        klog.ErrorS(err, "Failed to discover GPUs")
    }
````

系统发现包括：
- **CPU拓扑**：读取`/sys/devices/system/cpu/`下的拓扑信息，获取physical_package_id、die_id、core_id等
- **NUMA节点**：扫描`/sys/devices/system/node/`获取NUMA节点配置和内存信息
- **GPU设备**：通过PCI设备扫描识别GPU并确定其NUMA亲和性

### 资源层次设计

策略构建四层资源树结构，映射鲲鹏芯片架构：

````go
func (p *TopologyAwarePolicy) buildResourcePoolsByTopology() error {
    // 多 Socket 时，创建虚拟根节点
    if len(p.sys.PackageIDs()) > 1 {
        p.root = NewVirtualNode(p, "root", nilNode)
    }
    // 创建 Socket 节点
    sockets := p.createSocketNodes()
    // 创建 Die 节点
    dies := p.createDieNodes(sockets)
    // 创建 NUMA 节点
    p.createNumaNodes(sockets, dies)
````

**层次结构**：
1. **Root节点**：多Socket系统的虚拟根节点，统一管理全局资源
2. **Socket节点**：物理CPU包，对应鲲鹏芯片的一个物理插槽
3. **Die节点**：CPU包内的Die单元，鲲鹏芯片特有的中间层次
4. **NUMA节点**：最小的资源分配单元，包含CPU核心、内存和可能的GPU设备

### 分配策略设计

策略采用多维度评分机制，综合考虑资源容量、亲和性和负载均衡：

**评分维度**：
- **CPU容量评分**：基于可用CPU资源计算节点适配度
- **GPU亲和性评分**：GPU容器优先分配到GPU所在NUMA节点
- **内存容量评分**：确保节点有足够内存满足容器需求
- **负载均衡评分**：避免资源过度集中在单一节点

## 实现机制

### 静态树结构

系统启动时构建静态的四层资源树，每个节点维护完整的资源信息：
````go
type baseNode struct {
    policy       *TopologyAwarePolicy
    name         string
    kind         NodeKind
    depth        int
    parent       Node
    children     []Node
    nodeResource Supply
    freeResource Supply
}
````

**节点特性**：
- **层次关系**：每个节点明确其父子关系和在树中的深度
- **资源管理**：维护总资源量(nodeResource)和可用资源量(freeResource)
- **动态更新**：资源分配后实时更新节点状态

### NUMA亲和性实现

容器分配时，策略根据树结构实现精确的NUMA亲和性：

````go
func (p *TopologyAwarePolicy) allocatePool(containerCtx policy.ContainerContext) (Grant, error) {
    request := newRequest(containerCtx)
    // 计算资源亲和性
    affinity := p.calculatePoolAffinities(request)
    // 根据请求的资源，选择最优的资源池
    score, pools := p.sortPoolsByScore(request, affinity)
    // 选择出最优的资源池
    pool := p.findBestAvailablePool(request, pools)
````

### 资源排序与过滤

#### NUMA节点排序策略

策略实现多层次的节点排序算法：

1. **GPU优先策略**：GPU容器优先考虑GPU亲和性
2. **CPU容量排序**：按可用CPU资源降序排列
3. **内存容量排序**：确保内存资源充足
4. **负载均衡**：避免单点过载

````go
func (p *TopologyAwarePolicy) applyResourcePriorityStrategy(request Request, affinity map[int]int32, node1, node2 Node) (bool, bool) {
    switch p.resourcePriority {
    case policy.ResourcePriorityGPUFirst:
        // GPU优先：先比较GPU亲和性，再比较CPU容量
        if result, done := p.compareGPUAffinity(request, affinity, node1, node2); done {
            return result, true
        }
````

#### Socket资源过滤

系统在分配前进行多重过滤确保资源充足：

- **容量检查**：验证节点CPU和内存容量是否满足请求
- **亲和性过滤**：GPU容器过滤出有GPU亲和性的节点
- **深度优先**：优先选择层次更深（更具体）的节点

### 容器请求场景分析

#### 普通CPU容器
- **分配策略**：选择CPU资源最充足的NUMA节点
- **亲和性**：确保CPU集中在单一NUMA节点内
- **内存绑定**：启用内存拓扑时绑定对应NUMA内存

#### GPU计算容器
- **GPU发现**：通过环境变量识别GPU设备请求
- **亲和性计算**：计算GPU设备与NUMA节点的亲和关系
- **优先分配**：强制分配到GPU所在的NUMA节点

````go
func (p *TopologyAwarePolicy) findBestGPUAffinityPool(request Request, pools []Node) Node {
    // 计算GPU亲和性
    affinity := p.calculatePoolAffinities(request)
    // 分类池：有GPU亲和性的池和回退池
    gpuAffinityPools, fallbackPools := p.classifyPoolsByGPUAffinity(pools, affinity, request)
````

#### 系统容器
- **特殊处理**：kube-system命名空间容器直接分配到根节点
- **资源隔离**：避免与业务容器竞争关键资源

## 框架集成

### Hook机制

策略通过标准Hook接口与容器运行时集成：

````go
func (p *TopologyAwarePolicy) PreCreateContainerHook(ctx policy.HookContext) (*policy.Allocation, error) {
    containerCtx, ok := ctx.(*policy.ContainerContext)
    if err := p.AllocateResources(*containerCtx); err != nil {
        return nil, err
    }
    // 依据 Grant 的内容进行结果填充
    alloc := policy.NewAllocation()
    cpus := grant.SharedCPUSet().String()
    alloc.SetCPUSetCpus(cpus)
````

**关键Hook点**：
- **PreCreateContainerHook**：容器创建前进行资源分配
- **PostStopContainerHook**：容器停止后释放资源

### 策略注册

策略通过PolicyManager进行统一注册和管理：

````go
func (pm *policyManager) RegisterPolicy(policy Policy) {
    policyName := policy.Name()
    policy.SetCache(pm.cache)
    pm.policies[policyName] = policy
    klog.InfoS("Registered policy", "policy", policyName)
}
````

### 资源状态同步

#### 状态持久化
- **缓存机制**：资源分配状态持久化到缓存，支持重启恢复
- **状态同步**：实时同步容器资源使用情况到系统状态

#### 父节点传播
资源分配后向上传播使用情况，确保树状结构的一致性：

````go
func (p *TopologyAwarePolicy) propagateResourceUsageToParent(grant Grant) {
    currentNode := grant.GetNode()
    parent := currentNode.Parent()
    for parent != nil && !parent.IsNil() {
        p.updateParentResourceUsageByGrant(parent, grant)
        parent = parent.Parent()
    }
}
````

#### 监控集成
- **指标收集**：实时收集各节点资源使用指标
- **状态监控**：通过Prometheus指标暴露资源分配状态
- **异常检测**：监控资源分配异常并及时告警

### 生命周期管理

策略支持完整的生命周期管理：
- **初始化**：系统启动时自动发现硬件拓扑并构建资源树
- **运行时**：动态响应容器创建/删除请求，实时更新资源状态
- **恢复机制**：支持从缓存恢复资源分配状态，确保重启后的一致性

通过这套完整的设计与实现，Topology-Aware策略能够充分发挥鲲鹏芯片的硬件优势，为容器应用提供最优的资源分配和性能保障。

