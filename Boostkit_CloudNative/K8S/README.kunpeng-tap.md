# Kunpeng-TAP 编译和部署指南

## 概述

Kunpeng-TAP 是一个为鲲鹏处理器优化的容器拓扑感知调度组件，支持 NUMA 感知和拓扑感知的容器资源分配策略。

## 项目结构

```
├── cmd/kunpeng-tap/
│   ├── manager/          # TAP 管理器
│   └── proxy/            # TAP 代理
├── pkg/kunpeng-tap/
│   ├── cache/            # 缓存管理
│   ├── policy/           # 调度策略
│   ├── server/           # 服务器实现
│   ├── monitoring/       # 监控指标
│   └── version/          # 版本信息
├── api/kunpeng-tap/      # API 定义
├── test/kunpeng-tap/     # 测试代码
└── hack/kunpeng-tap/     # 部署脚本
```

## 编译

### 前置条件

- Go 1.23.6 或更高版本
- Linux 环境（推荐 Ubuntu 18.04+）

### 使用 Makefile 编译

```bash
# 查看所有可用命令
make -f Makefile.kunpeng-tap help

# 编译所有组件
make -f Makefile.kunpeng-tap build

# 仅编译管理器
make -f Makefile.kunpeng-tap build-manager

# 仅编译代理
make -f Makefile.kunpeng-tap build-proxy

# 清理编译产物
make -f Makefile.kunpeng-tap clean
```

### 手动编译

```bash
# 编译管理器
go build -o bin/kunpeng-tap-manager ./cmd/kunpeng-tap/manager

# 编译代理
go build -o bin/kunpeng-tap-proxy ./cmd/kunpeng-tap/proxy
```

## 开发

### 代码格式化和检查

```bash
# 格式化代码
make -f Makefile.kunpeng-tap fmt

# 代码静态检查
make -f Makefile.kunpeng-tap vet

# 整理依赖
make -f Makefile.kunpeng-tap tidy
```

### 运行测试

```bash
# 运行单元测试
make -f Makefile.kunpeng-tap test

# 运行端到端测试
make -f Makefile.kunpeng-tap test-e2e
```

### 本地运行

```bash
# 运行管理器
make -f Makefile.kunpeng-tap run-manager

# 运行代理
make -f Makefile.kunpeng-tap run-proxy
```

## 部署

### 系统服务部署

#### Docker 运行时环境

```bash
# 安装服务（Docker 运行时）
sudo make -f Makefile.kunpeng-tap install-service-docker

# 启动服务
sudo make -f Makefile.kunpeng-tap start-service

# 查看服务状态
sudo make -f Makefile.kunpeng-tap status-service
```

#### Containerd 运行时环境

```bash
# 安装服务（Containerd 运行时）
sudo make -f Makefile.kunpeng-tap install-service-containerd

# 启动服务
sudo make -f Makefile.kunpeng-tap start-service
```

### 服务管理

```bash
# 启动服务
sudo make -f Makefile.kunpeng-tap start-service

# 停止服务
sudo make -f Makefile.kunpeng-tap stop-service

# 重启服务
sudo make -f Makefile.kunpeng-tap restart-service

# 查看服务状态
sudo make -f Makefile.kunpeng-tap status-service

# 卸载服务
sudo make -f Makefile.kunpeng-tap uninstall-service
```

## 容器化部署

### 构建 Docker 镜像

```bash
# 构建镜像
make -f Makefile.kunpeng-tap docker-build

# 推送镜像
make -f Makefile.kunpeng-tap docker-push

# 多架构构建
make -f Makefile.kunpeng-tap docker-buildx
```

## 配置

### 配置选项

- `--runtime-proxy-endpoint`: 运行时代理端点（默认：/var/run/kunpeng-tap/runtimeproxy.sock）
- `--container-runtime-service-endpoint`: 容器运行时服务端点
- `--container-runtime-mode`: 容器运行时模式（Docker|Containerd）
- `--resource-policy`: 资源策略（numa-aware|topology-aware）
- `--resource-priority`: 资源分配优先级策略（cpu-first|gpu-first），仅适用于topology-aware策略，默认cpu-first
- `--enable-memory-topology`: 启用内存拓扑感知

## 资源优先级策略

Kunpeng-TAP 的 topology-aware 策略现在支持配置资源分配优先级，允许用户根据工作负载特性优化容器放置。

### CPU优先策略（默认）
```bash
./kunpeng-tap --resource-policy=topology-aware --resource-priority=cpu-first
```

**策略含义：**
- 在进行容器资源分配时，优先考虑CPU资源的可用性和容量
- 对于GPU工作负载，先选择CPU容量最佳的NUMA节点，再考虑GPU亲和性
- 适用于CPU密集型应用和混合工作负载环境

**适用场景：**
- CPU密集型工作负载（计算密集型应用、数据处理等）
- 通用应用程序和服务
- 需要保持向后兼容性的环境

### GPU优先策略
```bash
./kunpeng-tap --resource-policy=topology-aware --resource-priority=gpu-first
```

**策略含义：**
- 在进行容器资源分配时，优先考虑GPU设备的NUMA亲和性
- 对于GPU工作负载，优先选择GPU设备所在的NUMA节点，确保最佳的GPU-CPU数据传输性能
- 对于CPU-only工作负载，回退到CPU容量优先的分配策略

**适用场景：**
- 图形渲染和计算密集型GPU应用
- 对GPU-CPU数据传输延迟敏感的应用

### 实现原理

资源优先级策略通过调整topology-aware策略中节点选择算法的比较顺序来实现：

#### 节点选择算法流程
1. **资源容量检查**（不变）
   - 验证节点是否有足够的CPU、内存等基础资源
   - 不满足基础资源要求的节点直接排除

2. **拓扑深度比较**（不变）
   - 优先选择拓扑树中层级更低（更具体）的节点
   - 例如：NUMA节点优于Socket节点

3. **共享容量比较**（不变）
   - 比较节点的共享资源容量
   - 优先选择共享资源更充足的节点

4. **共置容器比较**（不变）
   - 考虑已分配容器的数量和资源使用情况
   - 优先选择负载较轻的节点

5. **资源优先级策略**（新增核心逻辑）
   - **CPU优先模式**：
     ```
     if (node1.cpuCapacity > node2.cpuCapacity) return node1
     if (node1.cpuCapacity == node2.cpuCapacity)
         return compareGPUAffinity(node1, node2)
     ```
   - **GPU优先模式**：
     ```
     if (hasGPURequest && node1.gpuAffinity > node2.gpuAffinity) return node1
     if (gpuAffinityEqual) return compareCPUCapacity(node1, node2)
     ```

6. **最终决胜**（不变）
   - 当所有其他条件相等时，选择ID较小的节点

#### GPU亲和性计算
- 检测容器环境变量中的GPU设备请求（`VA_VISIBLE_DEVICES`等）
- 查找请求的GPU设备在系统中的NUMA节点位置
- 计算每个候选NUMA节点与请求GPU设备的亲和性权重
- 亲和性权重越高，表示GPU-CPU数据传输性能越好

#### 策略切换的影响
- **向后兼容**：默认CPU优先策略保持原有行为
- **动态配置**：可通过命令行参数切换策略
- **工作负载感知**：策略会自动识别是否为GPU工作负载
- **性能优化**：GPU优先策略可显著提升GPU密集型应用的性能

### 使用示例

#### 场景1：混合工作负载环境
在同时运行CPU密集型和GPU密集型应用的环境中：

```bash
# 对于通用服务和CPU密集型应用
./kunpeng-tap --resource-policy=topology-aware --resource-priority=cpu-first

# 对于机器学习和GPU计算应用
./kunpeng-tap --resource-policy=topology-aware --resource-priority=gpu-first
```

#### 场景2：专用GPU集群
对于专门用于AI/ML训练的GPU集群：

```bash
# 启用GPU优先策略和内存拓扑感知
./kunpeng-tap --resource-policy=topology-aware \
               --resource-priority=gpu-first \
               --enable-memory-topology
```

#### 场景3：开发测试环境
对于需要保持兼容性的开发环境：

```bash
# 使用默认CPU优先策略
./kunpeng-tap --resource-policy=topology-aware
# 等同于
./kunpeng-tap --resource-policy=topology-aware --resource-priority=cpu-first
```

## 监控

Kunpeng-TAP 提供 Prometheus 指标，默认在 `:9091/metrics` 端点暴露。

## 故障排除

### 常见问题

1. **编译失败**
   - 确保 Go 版本 >= 1.23.6
   - 运行 `make -f Makefile.kunpeng-tap tidy` 更新依赖

2. **服务启动失败**
   - 检查容器运行时是否正常运行
   - 确保有足够的权限访问容器运行时套接字

3. **权限问题**
   - 确保以 root 权限运行服务安装命令
   - 检查 systemd 服务文件权限

### 日志查看

```bash
# 查看服务日志
sudo journalctl -u kunpeng-tap.service -f

# 查看服务状态
sudo systemctl status kunpeng-tap.service
```

## 版本信息

```bash
# 查看版本信息
./bin/kunpeng-tap --version
./bin/kunpeng-tap-manager --version
```

## 贡献

欢迎提交 Issue 和 Pull Request 来改进 Kunpeng-TAP。 